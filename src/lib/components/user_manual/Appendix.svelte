<div class="space-y-6 pl-4">
  <!-- Title -->
  <h1 class="text-2xl font-bold text-center">ภาคผนวก (Appendix)</h1>

  <!-- Customer Support Document -->
  <div class="space-y-2">
    <h2 class="text-xl font-semibold">เอกสารสนับสนุนลูกค้า (Customer Support)</h2>
    <p class="text-lg font-medium">แนวทางการสร้างเอกสาร (ตาราง Excel/CSV):</p>
    <ul class="list-disc list-outside ml-6 text-lg text-gray-700">
      <li>คอลัมน์แรกต้องชื่อ <code>question</code> และคอลัมน์ที่สองต้องชื่อ <code>answer</code></li>
      <li>ใช้ตัวอักษรพิมพ์เล็กทั้งหมด</li>
      <li>คำถาม-คำตอบอยู่ในแถวเดียวกัน</li>
    </ul>
    <img src="/user_manual/11-01-doc-excel-example.png" alt="ตัวอย่างเอกสาร Excel" class="rounded-lg border shadow-md mx-auto w-full max-w-3xl" />

    <p class="text-lg font-medium">แนวทางเอกสารแบบเอกสาร Doc/PDF:</p>
    <ul class="list-disc list-outside ml-6 text-lg text-gray-700">
      <li>จัดคำถามและคำตอบในสองแถวต่อกันแนวตั้ง</li>
      <li>ใช้คำว่า <code>question</code> และ <code>answer</code> เป็นตัวระบุ</li>
    </ul>
    <img src="/user_manual/11-02-doc-pdf-example.png" alt="ตัวอย่างเอกสาร PDF" class="rounded-lg border shadow-md mx-auto w-full max-w-3xl" />
  </div>

  <!-- Promotion Document -->
  <div class="space-y-2">
    <h2 class="text-xl font-semibold">เอกสารโปรโมชั่น (Promotion)</h2>
    <ul class="list-disc list-outside ml-6 text-lg text-gray-700">
      <li>รองรับไฟล์ PDF, JPG, PNG</li>
      <li>ต้องระบุวันเริ่มต้น และสิ้นสุดโปรโมชั่น</li>
    </ul>
    <img src="/user_manual/12-01-promo-1.png" alt="ตัวอย่างโปรโมชั่น 1" class="rounded-lg border shadow-md mx-auto w-full max-w-3xl" />
    <img src="/user_manual/12-02-promo-2.png" alt="ตัวอย่างโปรโมชั่น 2" class="rounded-lg border shadow-md mx-auto w-full max-w-3xl" />
    <img src="/user_manual/12-03-promo-3.png" alt="ตัวอย่างโปรโมชั่น 3" class="rounded-lg border shadow-md mx-auto w-full max-w-3xl" />
  </div>

  <!-- Product Document -->
  <div class="space-y-2">
    <h2 class="text-xl font-semibold">เอกสารผลิตภัณฑ์ (Product)</h2>
    <p class="text-lg font-medium">ตาราง Product:</p>
    <p class="text-lg text-gray-700">ตารางข้อมูลมีหลายคอลัมน์ เช่น <code>id</code>, <code>plan_id</code>, <code>name</code>, <code>coverage</code>, <code>premium</code> ฯลฯ พร้อมคำอธิบาย</p>

    <p class="text-lg font-medium">ตาราง Product Type:</p>
    <ul class="list-disc list-outside ml-6 text-lg text-gray-700">
      <li>CAR – ประกันยานพาหนะ</li>
      <li>HEALTH_ACCIDENT_TRAVEL – ประกันสุขภาพ/อุบัติเหตุ/การเดินทาง</li>
      <li>BUSINESS – ประกันธุรกิจ</li>
      <li>HOME – ประกันบ้าน</li>
      <li>... (มีรายละเอียดตามรหัส)</li>
    </ul>

    <p class="text-lg font-medium">ตัวอย่าง Product:</p>
    <img src="/user_manual/13-01-product-health.png" alt="ตัวอย่างผลิตภัณฑ์สุขภาพ" class="rounded-lg border shadow-md mx-auto w-full max-w-3xl" />
    <img src="/user_manual/13-02-table-health.png" alt="ตัวอย่างตาราง Health CSV" class="rounded-lg border shadow-md mx-auto w-full max-w-3xl" />

    <img src="/user_manual/14-01-product-car.png" alt="ตัวอย่างผลิตภัณฑ์รถยนต์" class="rounded-lg border shadow-md mx-auto w-full max-w-3xl" />
    <img src="/user_manual/14-02-table-car.png" alt="ตัวอย่างตาราง Car CSV" class="rounded-lg border shadow-md mx-auto w-full max-w-3xl" />

    <img src="/user_manual/15-01-product-compulsory.png" alt="ตัวอย่างผลิตภัณฑ์ พ.ร.บ." class="rounded-lg border shadow-md mx-auto w-full max-w-3xl" />
    <img src="/user_manual/15-02-table-compulsory.png" alt="ตัวอย่างตาราง พ.ร.บ." class="rounded-lg border shadow-md mx-auto w-full max-w-3xl" />

    <p class="text-gray-500 text-sm mt-4">* เวอร์ชันนี้ยังไม่รองรับรหัสพิเศษบางกลุ่ม เช่น 420, 520, 540 เป็นต้น</p>
  </div>
</div>
