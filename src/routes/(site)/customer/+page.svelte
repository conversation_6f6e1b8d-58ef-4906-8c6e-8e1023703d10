<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import { page } from '$app/stores';

	import {
		Table,
		TableHead,
		TableHeadCell,
		TableBody,
		TableBodyRow,
		TableBodyCell,
		Input,
		Tooltip,
		Indicator,
		Breadcrumb,
		BreadcrumbItem,
		Button,
		Dropdown,
		Checkbox
	} from 'flowbite-svelte';
	import { PhoneSolid } from 'flowbite-svelte-icons';
	import {
		UsersSolid,
		CaretDownSolid,
		CaretUpSolid,
		EditSolid,
		SearchOutline,
		ChevronDownOutline,
		AdjustmentsHorizontalSolid
	} from 'flowbite-svelte-icons';
	import type { PageData } from './$types';
	import Pagination from '$src/lib/components/UI/pagination.svelte';
	import { formatTimestamp, displayDate } from '$lib/utils';
	import { getColorClass } from '$lib/utils';
	import { CustomerService } from '$src/lib/api/features/customer/customers.service';
	import { onMount } from 'svelte';
	import type { CustomerInterface } from '$lib/api/types/customer';

	export let data: PageData;
	$: ({ customers: initialCustomers, error } = data);

	console.log(initialCustomers);


	// Always use server-side pagination
	let customers: CustomerInterface[] = [];
	let isLoading = false;

	$: role = $page.data.role;
	$: isAgent = role === 'Agent';

	// Filter state - make these directly reactive instead of nested in an object
	let selectedTags = new Set(['All']);
	let selectedPlatforms = new Set(['All']);

	// Filter options (loaded from API)
	let tagOptions: Array<{ id: number; name: string; color: string }> = [];
	let platformOptions: Array<{ value: string; label: string; color: string }> = [];

	// Available options with proper typing
	$: tagOptionsList = [
		{ name: 'All', id: 'All' },
		...tagOptions.sort((a: any, b: any) => a.name.localeCompare(b.name))
	];

	$: customerMatchedTags = customers?.map((customer) => ({
		id: customer.id,
		tags: customer.tags || [] // user_tags already contains full tag objects
	}));

	$: platformOptionsList = [
		{ value: 'All', label: 'All' },
		...platformOptions.sort((a: any, b: any) => (a.label || '').localeCompare(b.label || ''))
	];

	$: {console.warn("+page.svelte: platformOptionsList:", platformOptionsList)}

	function maskPhoneNumber(phone: string): string {
		if (!phone) return '';
		if (isAgent) {
			const len = phone.length;
			if (len <= 4) return phone;
			return phone.slice(0, 3) + 'x'.repeat(len - 5) + phone.slice(len - 2);
		}
		return phone;
	}

	let searchQuery = '';
	let sortColumn: keyof (typeof customers)[0] = 'customer_id';
	let sortDirection: 'asc' | 'desc' = 'asc';

	// Server-side ordering mapping
	const columnFieldMap = {
		customer_id: 'customer_id',
		name: 'name',
		email: 'email',
		platforms: 'first_platform_name' // Backend annotated field for platform sorting
	};

	let searchId = '';
	let searchPhone = '';
	let searchEmail = '';

	function sortBy(column: keyof (typeof customers)[0]) {
		handleServerSort(column);
	}

	// Server-side sorting
	function handleServerSort(column: string) {
		const backendField = columnFieldMap[column];
		if (!backendField) return;

		let ordering = backendField;
		if (sortColumn === column && sortDirection === 'asc') {
			ordering = '-' + backendField;
			sortDirection = 'desc';
		} else {
			sortDirection = 'asc';
		}
		sortColumn = column;

		currentPage = 1;
		fetchCustomers(ordering);
	}

	// Server-side fetch
	const customerService = new CustomerService();

	// async function fetchCustomers(ordering = 'customer_id') {
	// 	isLoading = true;

	// 	// Build filters with better debugging
	// 	const tagFilters = Array.from(selectedTags).filter((t) => t !== 'All');
	// 	const platformFilters = Array.from(selectedPlatforms).filter((p) => p !== 'All');

	// 	const filters = {
	// 		search: searchQuery.trim() || '',
	// 		tags: tagFilters.join(','),
	// 		platforms: platformFilters.join(','),
	// 		page: currentPage,
	// 		limit: itemsPerPage
	// 	};

	// 	console.log('=== FETCH CUSTOMERS DEBUG ===');
	// 	console.log('Selected filter values:');
	// 	console.log('- Selected Tags:', Array.from(selectedTags));
	// 	console.log('- Selected Platforms:', Array.from(selectedPlatforms));
	// 	console.log('- Search Query:', searchQuery);
	// 	console.log('Filters sent to API:', filters);
	// 	console.log('Current ordering:', ordering);
	// 	console.log('Current page:', currentPage);

	// 	try {
	// 		// Use token from server-side data
	// 		const token = data.token || '';

	// 		const response = await customerService.getCustomersWithFiltersAndOrdering(
	// 			token,
	// 			filters,
	// 			ordering
	// 		);

	// 		if (response.res_status === 200) {
	// 			// Handle paginated response structure
	// 			if (response.customers?.results) {
	// 				customers = response.customers.results;
	// 				totalItems = response.customers.count || 0;
	// 				totalPages = Math.ceil(totalItems / itemsPerPage);
	// 			} else {
	// 				// Fallback for direct array response
	// 				customers = Array.isArray(response.customers) ? response.customers : [];
	// 				totalItems = customers.length;
	// 				totalPages = 1;
	// 			}
	// 		} else {
	// 			console.error('Failed to fetch customers:', response.error_msg);
	// 			customers = [];
	// 		}
	// 	} catch (error) {
	// 		console.error('Error fetching customers:', error);
	// 		customers = [];
	// 	}

	// 	isLoading = false;
	// }

	// 4. Update your server-side fetch function to handle platform filtering
	async function fetchCustomers(ordering = 'customer_id') {
		isLoading = true;

		// Build filters with platform types
		const tagFilters = Array.from(selectedTags).filter((t) => t !== 'All');
		const platformFilters = Array.from(selectedPlatforms).filter((p) => p !== 'All');

		const filters = {
			search: searchQuery.trim() || '',
			tags: tagFilters.join(','),
			platforms: platformFilters.join(','), // This should be platform types like 'LINE,FACEBOOK'
			// platforms: 'FACEBOOK', // This should be platform types like 'LINE,FACEBOOK'
			page: currentPage,
			limit: itemsPerPage
		};

		console.log('=== FETCH CUSTOMERS DEBUG ===');
		console.log('Selected filter values:');
		console.log('- Selected Tags:', Array.from(selectedTags));
		console.log('- Selected Platforms:', Array.from(selectedPlatforms));
		console.log('- Search Query:', searchQuery);
		console.log('Filters sent to API:', filters);

		try {
			const token = data.token || '';
			const response = await customerService.getCustomersWithFiltersAndOrdering(
				token,
				filters,
				ordering
			);

			if (response.res_status === 200) {
				if (response.customers?.results) {
					customers = response.customers.results;
					totalItems = response.customers.count || 0;
					totalPages = Math.ceil(totalItems / itemsPerPage);
				} else {
					customers = Array.isArray(response.customers) ? response.customers : [];
					totalItems = customers.length;
					totalPages = 1;
				}
				
				// Reload filter options after getting new data
				await loadFilterOptions();
			} else {
				console.error('Failed to fetch customers:', response.error_msg);
				customers = [];
			}
		} catch (error) {
			console.error('Error fetching customers:', error);
			customers = [];
		}

		isLoading = false;
	}

	// Fetch unique platforms from API instead
	// 5. Helper function to get all unique platform types from current customers
	// function getUniquePlatformTypes(customers) {
	// 	const platforms = new Set();
	// 	customers.forEach(customer => {
	// 		if (customer.platforms) {
	// 			customer.platforms.forEach(platform => {
	// 				platforms.add(platform.platform);
	// 			});
	// 		}
	// 	});
	// 	return Array.from(platforms);
	// }

	// Debounced search
	
	let searchTimeout: ReturnType<typeof setTimeout>;
	function delayedSearch() {
		if (searchTimeout) clearTimeout(searchTimeout);
		searchTimeout = setTimeout(() => {
			currentPage = 1;
			fetchCustomers();
		}, 500);
	}

	$: searchQuery, delayedSearch();

	// function filterAll(item: any, term: string) {
	// 	const t = term.toLowerCase();
	// 	return [
	// 		String(item.customer_id), // ID
	// 		item.name ||
	// 			(item.first_name && item.last_name ? `${item.first_name} ${item.last_name}` : '') ||
	// 			item.line_user?.display_name, // Name
	// 		item.phone, // Phone
	// 		item.platforms?.name, // Platform
	// 		item.email, // Email
	// 		displayDate(item.created_on) // Created On
	// 	]
	// 		.filter(Boolean)
	// 		.some((field) => field.toLowerCase().includes(t));
	// }

	function filterAll(item: any, term: string) {
		const t = term.toLowerCase();
		
		// Get all platform types for this customer
		const platformTypes = item.platforms?.map(p => p.platform).join(' ') || '';
		
		return [
			String(item.customer_id), // ID
			item.name ||
				(item.first_name && item.last_name ? `${item.first_name} ${item.last_name}` : '') ||
				item.line_user?.display_name, // Name
			item.phone, // Phone
			platformTypes, // All platform types
			item.email, // Email
			displayDate(item.created_on) // Created On
		]
			.filter(Boolean)
			.some((field) => field.toLowerCase().includes(t));
	}

	function compare(a: any, b: any) {
		let av = a[sortColumn],
			bv = b[sortColumn];
		if (sortColumn === 'created_on') {
			av = new Date(av).getTime();
			bv = new Date(bv).getTime();
		}
		if (typeof av === 'number' && typeof bv === 'number') {
			return sortDirection === 'asc' ? av - bv : bv - av;
		}
		return sortDirection === 'asc'
			? String(av).localeCompare(String(bv))
			: String(bv).localeCompare(String(av));
	}

	// Calculate statistics
	$: totalCustomers = totalItems || customers?.length || 0;

	//////////////// Pagination Logic ////////////////
	// pagination state variables
	let currentPage = 1;
	let itemsPerPage = 10;
	let totalItems = 0;
	let totalPages = 1;

	// Update pagination calculation
	$: totalPages = Math.ceil(totalItems / itemsPerPage) || 1;


	// Update the client-side filtering logic (if you need it for immediate filtering)
	$: filteredCustomers = customers.filter(customer => {
		// Platform filter
		if (!selectedPlatforms.has('All')) {
			const customerPlatformTypes = customer.platforms?.map(p => p.platform) || [];
			const hasMatchingPlatform = Array.from(selectedPlatforms).some(selectedPlatform => 
				customerPlatformTypes.includes(selectedPlatform)
			);
			if (!hasMatchingPlatform) return false;
		}
		
		// Tag filter
		if (!selectedTags.has('All')) {
			const customerTagNames = customer.tags?.map(t => t.name) || [];
			const hasMatchingTag = Array.from(selectedTags).some(selectedTag => 
				customerTagNames.includes(selectedTag)
			);
			if (!hasMatchingTag) return false;
		}
		
		// Search filter
		if (searchQuery.trim()) {
			return filterAll(customer, searchQuery);
		}
		
		return true;
	});


	// paginatedCustomers
	$: paginatedCustomers = filteredCustomers;

	// $: paginatedCustomers = customers;

	function updateCurrentPage(newCurrentPage: number) {
		currentPage = newCurrentPage;
		fetchCustomers();
	}

	// Filter toggle functions
	function toggleTag(tag: { name: string; id: string | number }) {
		const newSet = new Set(selectedTags);
		const tagName = tag.name;

		if (tagName === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(tagName)) {
				newSet.delete(tagName);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(tagName);
			}
		}

		selectedTags = newSet;
		currentPage = 1;
		fetchCustomers();
	}

	function togglePlatform(platform: { value: string; label: string }) {
		const newSet = new Set(selectedPlatforms);
		const platformName = platform.label;

		if (platformName === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(platformName)) {
				newSet.delete(platformName);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(platformName);
			}
		}

		selectedPlatforms = newSet;
		currentPage = 1;
		fetchCustomers();
	}

	function resetFilters() {
		selectedTags = new Set(['All']);
		selectedPlatforms = new Set(['All']);
		searchQuery = '';
		currentPage = 1;
		fetchCustomers();
	}

	// Load filter options
	// async function loadFilterOptions() {
	// 	try {
	// 		const token = data.token || '';

	// 		// Load tags
	// 		const tagsResponse = await customerService.getFilterTags(token);
	// 		if (tagsResponse.res_status === 200) {
	// 			tagOptions = tagsResponse.data;
	// 		}

	// 		// Load platforms
	// 		const platformsResponse = await customerService.getFilterPlatforms(token);
	// 		if (platformsResponse.res_status === 200) {
	// 			platformOptions = platformsResponse.data;
	// 		}
	// 	} catch (error) {
	// 		console.error('Error loading filter options:', error);
	// 	}
	// }

	async function loadFilterOptions() {
		try {
			const token = data.token || '';

			// Load tags
			const tagsResponse = await customerService.getFilterTags(token);
			if (tagsResponse.res_status === 200) {
				tagOptions = tagsResponse.data;
			}

			// Load platforms - get unique platform types from customers
			const platformsResponse = await customerService.getFilterPlatforms(token);
				if (platformsResponse.res_status === 200) {
					platformOptions = platformsResponse.data;
				}
			
			// if (customers && customers.length > 0) {
			// 	const uniquePlatforms = new Set();
			// 	customers.forEach(customer => {
			// 		if (customer.platforms) {
			// 			customer.platforms.forEach(platform => {
			// 				uniquePlatforms.add(platform.platform);
			// 			});
			// 		}
			// 	});
			// 	platformOptions = Array.from(uniquePlatforms).map(platform => ({
			// 		id: platform,
			// 		name: platform,
			// 		color: '' // You can add color logic if needed
			// 	}));
			// } else {
			// 	// Fallback: load from API if available
			// 	const platformsResponse = await customerService.getFilterPlatforms(token);
			// 	if (platformsResponse.res_status === 200) {
			// 		platformOptions = platformsResponse.data;
			// 	}
			// }
		} catch (error) {
			console.error('Error loading filter options:', error);
		}
	}


	// Format tag name to capitalize first letter
	// TODO - I remember I use this filter explicitly once in the code, but I cannot find it
	// Use this function instead of inline formatting if found
	function formatTagName(tag: string): string {
		// return tag.charAt(0).toUpperCase() + tag.slice(1);
		return tag.charAt(0).toUpperCase() + tag.slice(1).split('_').join(' ');
	}

	// Initialize
	onMount(() => {
		loadFilterOptions();
		fetchCustomers();
	});
</script>

<svelte:head>
	<title>{t('customers')}</title>
</svelte:head>

<div class="flex h-screen">
	<div class="w-full overflow-x-auto overflow-y-auto bg-white p-8">
		<Breadcrumb aria-label="Default breadcrumb example" class="mb-3">
			<BreadcrumbItem href="/" home>
				<span class="text-gray-400">{t('home')}</span>
			</BreadcrumbItem>
			<BreadcrumbItem>
				<span class="text-gray-700">{t('customers')}</span>
			</BreadcrumbItem>
		</Breadcrumb>

		<div class="mb-6">
			<h2 class="text-2xl font-bold">{t('customers')}</h2>
			<p class="text-gray-600">{totalCustomers} {t('customers')}</p>
		</div>

		<!-- Filters and Search Bar - Improved Layout -->
		<div
			class="mb-6 flex flex-col items-start gap-4 lg:flex-row lg:items-center lg:justify-between"
		>
			<!-- Left side - Filter Buttons -->
			<div class="flex flex-wrap gap-3">
				<!-- Tag Filter -->
				<div>
					<Button
						color={!selectedTags.has('All') ? 'dark' : 'none'}
						class={`${!selectedTags.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_tag')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown class="max-h-80 w-72 overflow-y-auto p-2 shadow-lg">
						{#each tagOptionsList as tag}
							<div class="flex items-center justify-between rounded p-2 hover:bg-gray-100">
								<Checkbox
									checked={selectedTags.has(tag.name)}
									on:change={() => toggleTag(tag)}
									class="flex items-center"
								>
									<span class="ml-2 text-sm">
										{tag.name === 'All' ? tag.name : formatTagName(tag.name)}
									</span>
								</Checkbox>
							</div>
						{/each}
					</Dropdown>
				</div>

				<!-- Platform Filter -->
				<div>
					<Button
						color={!selectedPlatforms.has('All') ? 'dark' : 'none'}
						class={`${!selectedPlatforms.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_platform')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown class="max-h-80 w-72 overflow-y-auto p-2 shadow-lg">
						{#each platformOptionsList as platform}
							<div class="flex items-center justify-between rounded p-2 hover:bg-gray-100">
								<Checkbox
									checked={selectedPlatforms.has(platform.label)}
									on:change={() => togglePlatform(platform)}
									class="flex items-center"
								>
									<span class="ml-2 flex items-center gap-2 text-sm">
										{#if platform.value !== 'All'}
											<span class={`flex items-center gap-2 rounded border px-2 py-1 text-xs`}>
												{#if platform.value === 'TELEPHONE'}
													<PhoneSolid class="h-5 w-5" />
												{:else if platform.value === 'LINE'}
													<img
														src="/images/platform-line.png"
														alt="LINE Icon"
														class="h-5 w-5 rounded-full"
													/>
												{:else if platform.value === 'FACEBOOK'}
													<img
														src="/images/platform-facebook.png"
														alt="Facebook Icon"
														class="h-5 w-5 rounded-full"
													/>
												{:else if platform.value === 'WHATSAPP'}
													<img
														src="/images/platform-whatsapp.png"
														alt="WhatsApp Icon"
														class="h-5 w-5 rounded-full"
													/>
												{:else if platform.value === 'INSTAGRAM'}
													<img
														src="/images/platform-instagram.png"
														alt="Instagram Icon"
														class="h-5 w-5 rounded-full"
													/>
												{:else if platform.value === 'TELEGRAM'}
													<img
														src="/images/platform-telegram.png"
														alt="Telegram Icon"
														class="h-5 w-5 rounded-full"
													/>
												{/if}
												{platform.label}
											</span>
										{:else}
											{platform.label}
										{/if}
									</span>
								</Checkbox>
							</div>
						{/each}
					</Dropdown>
				</div>

				<!-- Reset Filter -->
				<Button
					color="none"
					on:click={resetFilters}
					class="w-auto border shadow-md hover:bg-gray-100"
				>
					{t('filter_reset')}
				</Button>

				<!-- Loading indicator -->
				{#if isLoading}
					<div class="flex items-center gap-2">
						<span class="text-sm text-gray-500">{t('loading')}</span>
					</div>
				{/if}
			</div>

			<!-- Right side - Search Bar -->
			<div class="relative w-full shadow-md lg:w-1/3">
				<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
					<SearchOutline class="h-5 w-5 text-gray-500" />
				</div>
				<Input
					id="searchBar"
					type="text"
					placeholder={t('customer_search_placeholder')}
					bind:value={searchQuery}
					class={`block w-full rounded-lg border bg-white py-2.5 pl-10
                        focus:border-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-700
                        ${searchQuery ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300'}`}
				/>
			</div>
		</div>

		{#if error}
			<p class="text-red-600">{error}</p>
		{:else}
			<Table sort={compare} hoverable shadow class="w-full table-fixed">
				<TableHead>
					<TableHeadCell class="w-[70px]" on:click={() => sortBy('customer_id')}>
						<div class="flex items-center">
							{t('table_no')}
							{#if sortColumn === 'customer_id'}
								{#if sortDirection === 'asc'}
									<CaretUpSolid class="ml-1 h-4 w-4" />
								{:else}
									<CaretDownSolid class="ml-1 h-4 w-4" />
								{/if}
							{/if}
						</div>
					</TableHeadCell>

					<TableHeadCell class="w-[250px]" on:click={() => sortBy('name')}>
						<div class="flex items-center">
							{t('table_name')}
							{#if sortColumn === 'name'}
								{#if sortDirection === 'asc'}
									<CaretUpSolid class="ml-1 h-4 w-4" />
								{:else}
									<CaretDownSolid class="ml-1 h-4 w-4" />
								{/if}
							{/if}
						</div>
					</TableHeadCell>

					<TableHeadCell class="w-[150px]">
						{t('tag')}
					</TableHeadCell>

					<TableHeadCell class="w-[150px]" on:click={() => sortBy('platforms')}>
						<div class="flex items-center">
							{t('table_platform')}
							{#if sortColumn === 'platforms'}
								{#if sortDirection === 'asc'}
									<CaretUpSolid class="ml-1 h-4 w-4" />
								{:else}
									<CaretDownSolid class="ml-1 h-4 w-4" />
								{/if}
							{/if}
						</div>
					</TableHeadCell>

					<TableHeadCell class="w-[180px]">{t('table_phone')}</TableHeadCell>

					<TableHeadCell class="w-[280px]" on:click={() => sortBy('email')}>
						<div class="flex items-center">
							{t('table_email')}
							{#if sortColumn === 'email'}
								{#if sortDirection === 'asc'}
									<CaretUpSolid class="ml-1 h-4 w-4" />
								{:else}
									<CaretDownSolid class="ml-1 h-4 w-4" />
								{/if}
							{/if}
						</div>
					</TableHeadCell>

					<!-- <TableHeadCell class="w-[150px]" on:click={() => sortBy('created_on')}>{t('table_created_on')}</TableHeadCell> -->
				</TableHead>

				<TableBody>
					{#if paginatedCustomers.length === 0}
						<TableBodyRow>
							<TableBodyCell colspan={9} class="py-4 text-center text-gray-500">
								{t('table_no_data')}
							</TableBodyCell>
						</TableBodyRow>
					{:else}
						{#each paginatedCustomers as item}
							<TableBodyRow slot="row">
								<TableBodyCell>
									<a
										href={`/customer/${item.customer_id}`}
										class="flex items-center py-2 text-blue-600 hover:underline"
									>
										{item.customer_id}
										<EditSolid class="ml-1 h-4 w-4" />
									</a>
								</TableBodyCell>

								<TableBodyCell>
									<span class="break-words">
										<!-- {item.name ? item.name : item.line_user ? item.name : '-'} -->
										{item.first_name && item.last_name
											? `${item.first_name} ${item.last_name}`
											: item.name || '-'}
									</span>

									<!-- <div class="truncate text-xs text-gray-500">{item.name}</div> -->
								</TableBodyCell>

								<!-- Fixed Tag Display Section -->
								<TableBodyCell>
									{#if item.tags && item.tags.length > 0}
										{#if item.tags.length === 1}
											<!-- Single tag display -->
											<span class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm">
												<Indicator
													size="sm"
													class={`mr-1 ${getColorClass(item.tags[0].color)} inline-block`}
												/>
												{formatTagName(item.tags[0].name)}
											</span>
										{:else}
											<!-- Multiple tags display -->
											<div class="relative inline-block">
												<span
													class="inline-flex cursor-pointer items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm"
													data-popover-target="popover-tags-{item.id}"
												>
													<span class="flex items-center gap-1">
														<span class="relative flex -space-x-1">
															{#each item.tags.slice(0, 3) as tag, i (tag.name)}
																<Indicator
																	size="sm"
																	class={`${getColorClass(tag.color)}`}
																	style="z-index: {10 - i};"
																/>
															{/each}
														</span>
														{item.tags.length}
														{t('labels')}
													</span>
												</span>

												<!-- Tooltip for all tags -->
												<Tooltip triggeredBy="[data-popover-target='popover-tags-{item.id}']">
													<div class="max-w-xs px-2 py-1">
														<ul class="space-y-1">
															{#each item.tags as tag}
																<li class="flex items-center gap-1">
																	<Indicator
																		size="sm"
																		class={`mr-1 ${getColorClass(tag.color)}`}
																	/>
																	{formatTagName(tag.name)}
																</li>
															{/each}
														</ul>
													</div>
												</Tooltip>
											</div>
										{/if}
									{:else}
										<!-- No tags -->
										<!-- <span class="italic text-gray-500">{t('no_tags')}</span> -->
										-
									{/if}
								</TableBodyCell>
							
								<TableBodyCell>
									{#if item.platforms && item.platforms.length > 0}
										{@const platformGroups = item.platforms.reduce((acc, platform) => {
											if (!acc[platform.platform]) {
												acc[platform.platform] = [];
											}
											acc[platform.platform].push(platform.channel_name);
											return acc;
										}, {})}
										{@const uniquePlatforms = Object.keys(platformGroups)}
										
										<div
											class="inline-flex items-center justify-center gap-1 rounded border px-2 py-1 text-xs cursor-help"
											id="platform-{item.customer_id}"
										>
											{#each uniquePlatforms as platformType}
												{#if platformType === 'LINE'}
													<img
														src="/images/platform-line.png"
														alt="LINE Icon"
														class="h-5 w-5"
													/>
												{:else if platformType === 'Facebook Messenger'}
													<img
														src="/images/platform-messenger.png"
														alt="Facebook Icon"
														class="h-5 w-5"
													/>
												<!-- {:else if platformType === 'Telephone'}
													<PhoneSolid class="h-5 w-5" /> -->
												{:else if platformType === 'WhatsApp'}
													<img
														src="/images/platform-whatsapp.png"
														alt="WhatsApp Icon"
														class="h-5 w-5"
													/>
												{:else if platformType === 'Instagram'}
													<img
														src="/images/platform-instagram.png"
														alt="Instagram Icon"
														class="h-5 w-5"
													/>
												{:else if platformType === 'Telegram'}
													<img
														src="/images/platform-telegram.png"
														alt="Telegram Icon"
														class="h-5 w-5"
													/>
												{/if}
											{/each}
										</div>
										
										<Tooltip triggeredBy="#platform-{item.customer_id}" placement="top" class="z-50">
											<div class="space-y-2 max-w-xs">
												{#each Object.entries(platformGroups) as [platform, channels]}
													<div>
														<div class="font-medium text-sm">{platform}</div>
														<div class="text-sm space-y-1">
															{#each channels as channel}
																<div>{channel}</div>
															{/each}
														</div>
													</div>
												{/each}
											</div>
										</Tooltip>
									{:else}
										<div class="inline-flex items-center justify-center gap-2 rounded border px-2 py-1 text-xs">
											-
										</div>
									{/if}
								</TableBodyCell>

								<TableBodyCell>{item.phone ? maskPhoneNumber(item.phone) : '-'}</TableBodyCell>

								<TableBodyCell>
									<span class="break-words">
										{item.email ?? '-'}
									</span>
								</TableBodyCell>
								<!-- <TableBodyCell>
                                    <div>{displayDate(item.created_on).date}</div>
                                    <div>{displayDate(item.created_on).time}</div>
                                </TableBodyCell> -->
							</TableBodyRow>
						{/each}
					{/if}
				</TableBody>
			</Table>

			<!-- Pagination Layout -->
			<Pagination {currentPage} {totalPages} visibleCount={10} {updateCurrentPage} />
		{/if}
	</div>
</div>

<style>
	.break-words {
		word-break: break-word;
		white-space: normal;
	}
</style>
