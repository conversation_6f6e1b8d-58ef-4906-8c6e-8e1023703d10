<script lang="ts">
    import { 
        Sidebar, 
        SidebarGroup, 
        SidebarWrapper, 
        SidebarDropdownItem, 
        SidebarDropdownWrapper 
    } from "flowbite-svelte";

    // Mapping route key to component modules
	let selected = '';    
    let componentMap = {
        // Login
        login_account: () => import('$lib/components/user_manual/LoginAccount.svelte'),
        create_account: () => import('$lib/components/user_manual/CreateAccount.svelte'),

        // Settings > General
        site_settings: () => import('$lib/components/user_manual/SiteSettings.svelte'),
        survey_upload: () => import('$lib/components/user_manual/SurveyUpload.svelte'),
        company_info: () => import('$lib/components/user_manual/CompanyInfo.svelte'),
        webhook_connect: () => import('$lib/components/user_manual/WebhookConnect.svelte'),
        ai_chatbot_settings: () => import('$lib/components/user_manual/AIChatbotSettings.svelte'),

        // Settings > Team Management
        partner_settings: () => import('$lib/components/user_manual/PartnerSettings.svelte'),
        department_settings: () => import('$lib/components/user_manual/DepartmentSettings.svelte'),
        staff_tags: () => import('$lib/components/user_manual/StaffTags.svelte'),
        customer_tags: () => import('$lib/components/user_manual/CustomerTags.svelte'),
        ticket_transfer: () => import('$lib/components/user_manual/TicketTransfer.svelte'),
        business_hours: () => import('$lib/components/user_manual/BusinessHours.svelte'),

        // Settings > Accounts
        update_profile: () => import('$lib/components/user_manual/UpdateProfile.svelte'),
        set_self_schedule: () => import('$lib/components/user_manual/SetSelfSchedule.svelte'),

        // Users
        edit_profile: () => import('$lib/components/user_manual/EditProfile.svelte'),
        manage_partner: () => import('$lib/components/user_manual/ManagePartner.svelte'),
        set_roles: () => import('$lib/components/user_manual/SetRoles.svelte'),
        set_work_shift: () => import('$lib/components/user_manual/SetWorkShift.svelte'),

        // Chat-center
        chat_with_customer: () => import('$lib/components/user_manual/ChatWithCustomer.svelte'),
        transfer_ticket: () => import('$lib/components/user_manual/TransferTicket.svelte'),
        close_ticket: () => import('$lib/components/user_manual/CloseTicket.svelte'),
        reopen_ticket: () => import('$lib/components/user_manual/ReopenTicket.svelte'),
        change_priority: () => import('$lib/components/user_manual/ChangePriority.svelte'),
        edit_customer_info: () => import('$lib/components/user_manual/EditCustomerInfo.svelte'),
        edit_customer_tags: () => import('$lib/components/user_manual/EditCustomerTags.svelte'),
        add_customer_note: () => import('$lib/components/user_manual/AddCustomerNote.svelte'),

        // Customers
        edit_customer: () => import('$lib/components/user_manual/EditCustomer.svelte'),
        add_note_from_customer: () => import('$lib/components/user_manual/AddNoteFromCustomer.svelte'),

        // Tickets
        ticket_transfer_repeat: () => import('$lib/components/user_manual/TicketTransferRepeat.svelte'),
        ticket_close_repeat: () => import('$lib/components/user_manual/TicketCloseRepeat.svelte'),
        reopen_ticket_repeat: () => import('$lib/components/user_manual/ReopenTicketRepeat.svelte'),
        priority_change_repeat: () => import('$lib/components/user_manual/PriorityChangeRepeat.svelte'),

        // Knowledge Base
        upload_doc: () => import('$lib/components/user_manual/UploadDoc.svelte'),
        download_doc: () => import('$lib/components/user_manual/DownloadDoc.svelte'),
        delete_doc: () => import('$lib/components/user_manual/DeleteDoc.svelte'),

        // Dashboard
        team_performance: () => import('$lib/components/user_manual/TeamPerformance.svelte'),
        quality_dashboard: () => import('$lib/components/user_manual/QualityDashboard.svelte'),

        // Appendix
        appendix: () => import('$lib/components/user_manual/Appendix.svelte')
    };


	let LoadedComponent = null;

	// When user selects a menu item
	async function loadComponent(key: string) {
		selected = key;
		if (componentMap[key]) {
			const module = await componentMap[key]();
			LoadedComponent = module.default;
		}
	}
</script>

<div class="flex h-screen bg-gray-50"> 
    <div class="w-[270px] overflow-y-auto bg-gray-50 border-r max-h-screen">
        <Sidebar class="min-h-screen bg-gray-50">
            <SidebarWrapper>
                <SidebarGroup>
                    <SidebarDropdownWrapper label="การเข้าใช้งาน" isOpen={true}>
                        <SidebarDropdownItem label="• การเข้าสู่ระบบ" on:click={() => loadComponent('login_account')} />
                        <SidebarDropdownItem label="• วิธีการสร้างบัญชีเพิ่ม" on:click={() => loadComponent('create_account')} />
                    </SidebarDropdownWrapper>

                    <SidebarDropdownWrapper label="การตั้งค่าระบบ" isOpen={true}>
                        <SidebarDropdownWrapper label="• การตั้งค่าทั่วไป" isOpen={true}>
                            <SidebarDropdownItem label="• วิธีการตั้งค่ารูปแบบเว็บไซต์" on:click={() => loadComponent('site_settings')} />
                            <SidebarDropdownItem label="• วิธีการอัปโหลดรูปแบบสำรวจความพึงพอใจ" on:click={() => loadComponent('survey_upload')} />
                            <SidebarDropdownItem label="• วิธีการตั้งค่าข้อมูลบริษัท" on:click={() => loadComponent('company_info')} />
                            <SidebarDropdownItem label="• วิธีการเชื่อมต่อ Webhook สำหรับแพลตฟอร์มโซเชียลมีเดีย" on:click={() => loadComponent('webhook_connect')} />
                            <SidebarDropdownItem label="• วิธีการตั้งค่าแชทบอทเอไอ" on:click={() => loadComponent('ai_chatbot_settings')} />
                        </SidebarDropdownWrapper>

                        <SidebarDropdownWrapper label="• การจัดการทีม" isOpen={true}>
                            <SidebarDropdownItem label="• วิธีการจัดการพาร์ทเนอร์" on:click={() => loadComponent('partner_settings')} />
                            <SidebarDropdownItem label="• วิธีการตั้งค่าแผนก" on:click={() => loadComponent('department_settings')} />
                            <SidebarDropdownItem label="• วิธีการจัดการแท็กเฉพาะทางของพนักงาน" on:click={() => loadComponent('staff_tags')} />
                            <SidebarDropdownItem label="• วิธีการจัดการแท็กของลูกค้า" on:click={() => loadComponent('customer_tags')} />
                            <SidebarDropdownItem label="• วิธีการตั้งค่าการโอนทิกเก็ต" on:click={() => loadComponent('ticket_transfer')} />
                            <SidebarDropdownItem label="• วิธีการตั้งค่าเวลาทำการของบริษัท" on:click={() => loadComponent('business_hours')} />
                        </SidebarDropdownWrapper>   

                        <SidebarDropdownWrapper label="• การใช้หน้าบัญชีผู้ใช้" isOpen={true}>
                            <SidebarDropdownItem label="• วิธีการอัปเดตข้อมูลส่วนตัว" on:click={() => loadComponent('update_profile')} />
                            <SidebarDropdownItem label="• วิธีการตั้งกะเวลาทำงานของตนเอง" on:click={() => loadComponent('set_self_schedule')} />
                        </SidebarDropdownWrapper>   
                    </SidebarDropdownWrapper>

                    <SidebarDropdownWrapper label="การใช้งานหน้าผู้ใช้" isOpen={true}>
                        <SidebarDropdownItem label="• วิธีการเปลี่ยนหรือแก้ไขประวัติส่วนตัว" on:click={() => loadComponent('edit_profile')} />
                        <SidebarDropdownItem label="• วิธีการเพิ่มหรือลดพาร์ทเนอร์" on:click={() => loadComponent('manage_partner')} />
                        <SidebarDropdownItem label="• วิธีการตั้งบทบาทหน้าที่" on:click={() => loadComponent('set_roles')} />
                        <SidebarDropdownItem label="• วิธีการตั้งกะเวลาทำงาน" on:click={() => loadComponent('set_work_shift')} />
                    </SidebarDropdownWrapper>

                    <SidebarDropdownWrapper label="การใช้งานหน้าแชท" isOpen={true}>
                        <SidebarDropdownItem label="• วิธีการสนทนากับลูกค้า" on:click={() => loadComponent('chat_with_customer')} />
                        <SidebarDropdownItem label="• วิธีการโอนทิกเก็ตให้พนักงานอื่น" on:click={() => loadComponent('transfer_ticket')} />
                        <SidebarDropdownItem label="• วิธีการปิดทิกเก็ต" on:click={() => loadComponent('close_ticket')} />
                        <SidebarDropdownItem label="• วิธีการกลับมามอบหมายทิกเก็ตที่ถูกปิดไปแล้ว" on:click={() => loadComponent('reopen_ticket')} />
                        <SidebarDropdownItem label="• วิธีเปลี่ยนลำดับความสำคัญ" on:click={() => loadComponent('change_priority')} />
                        <SidebarDropdownItem label="• วิธีแก้ไขข้อมูลลูกค้า" on:click={() => loadComponent('edit_customer_info')} />
                        <SidebarDropdownItem label="• วิธีการแก้ไขแท็กของลูกค้า" on:click={() => loadComponent('edit_customer_tags')} />
                        <SidebarDropdownItem label="• วิธีเพิ่มโน๊ตของลูกค้า" on:click={() => loadComponent('add_customer_note')} />
                    </SidebarDropdownWrapper>

                    <SidebarDropdownWrapper label="การใช้งานหน้าลูกค้า" isOpen={true}>
                        <SidebarDropdownItem label="• วิธีแก้ไขข้อมูลลูกค้า" on:click={() => loadComponent('edit_customer')} />
                        <SidebarDropdownItem label="• วิธีเพิ่มโน๊ตผ่านลูกค้า" on:click={() => loadComponent('add_note_from_customer')} />
                    </SidebarDropdownWrapper>   

                    <SidebarDropdownWrapper label="การใช้งานหน้าทิกเก็ต" isOpen={true}>
                        <SidebarDropdownItem label="• วิธีการโอนทิกเก็ตให้พนักงานอื่น" on:click={() => loadComponent('ticket_transfer_repeat')} />
                        <SidebarDropdownItem label="• วิธีการปิดทิกเก็ต" on:click={() => loadComponent('ticket_close_repeat')} />
                        <SidebarDropdownItem label="• วิธีการกลับมามอบหมายทิกเก็ตที่ถูกปิดไปแล้ว" on:click={() => loadComponent('reopen_ticket_repeat')} />
                        <SidebarDropdownItem label="• วิธีเปลี่ยนลำดับความสำคัญ" on:click={() => loadComponent('priority_change_repeat')} />
                    </SidebarDropdownWrapper>

                    <SidebarDropdownWrapper label="การอัพโหลดเอกสาร" isOpen={true}>
                        <SidebarDropdownItem label="• วิธีการอัพโหลดเอกสาร" on:click={() => loadComponent('upload_doc')} />
                        <SidebarDropdownItem label="• วิธีดาวน์โหลดเอกสาร" on:click={() => loadComponent('download_doc')} />
                        <SidebarDropdownItem label="• วิธีลบเอกสาร" on:click={() => loadComponent('delete_doc')} />
                    </SidebarDropdownWrapper>

                    <SidebarDropdownWrapper label="การดูหน้าแดชบอร์ด" isOpen={true}>
                        <SidebarDropdownItem label="• หน้าประสิทธิภาพทีม" on:click={() => loadComponent('team_performance')} />
                        <SidebarDropdownItem label="• หน้าคุณภาพงาน" on:click={() => loadComponent('quality_dashboard')} />
                    </SidebarDropdownWrapper>

                    <SidebarDropdownWrapper label="ภาคผนวก" isOpen={true}>
                        <SidebarDropdownItem label="• เอกสาร" on:click={() => loadComponent('appendix')} />
                    </SidebarDropdownWrapper>

                </SidebarGroup>
            </SidebarWrapper>
        </Sidebar>
    </div>

    <div class="flex-1 overflow-y-auto p-10 bg-white max-h-screen">
        {#if LoadedComponent}
            <svelte:component this={LoadedComponent} />
        {:else}
            <p class="text-gray-400 mt-0 self-start">กรุณาเลือกเมนูจากด้านซ้าย</p>
        {/if}
    </div>
</div>