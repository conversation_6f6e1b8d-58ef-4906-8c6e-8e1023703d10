<script>
	import { t } from '$lib/stores/i18n';
	import { onMount } from 'svelte';
	import { Alert, Toggle, Checkbox, Button, Select, Label } from 'flowbite-svelte';
	import { invalidateAll } from '$app/navigation';
	import { enhance } from '$app/forms';
	import { toastStore } from '$lib/stores/toastStore';
	import { CheckOutline } from 'flowbite-svelte-icons';

	// export let dominantColor;
	export let user_schedule;
	export let business_hours;

	// our reactive state
	let days = [];
	let sameAsBusinessHours = false;

	// Change tracking state
	let formHasUnsavedChanges = false;

	// Time validation state
	let timeValidationErrors = {};
	let hasValidationErrors = false;

	// Day selection validation
	let hasNoDaysSelected = false;
	let daySelectionError = '';

	// build your time dropdown
	const timeOptions = Array.from({ length: 48 }, (_, i) => {
		const hour = Math.floor(i / 2)
			.toString()
			.padStart(2, '0');
		const minutes = i % 2 === 0 ? '00' : '30';
		return `${hour}:${minutes}`;
	});

	// Function to convert time string (HH:MM) to minutes since midnight for comparison
	function timeToMinutes(timeStr) {
		if (!timeStr) return 0;
		const [hours, minutes] = timeStr.split(':').map(Number);
		return hours * 60 + minutes;
	}

	// Function to check if two time ranges overlap
	function timeRangesOverlap(start1, end1, start2, end2) {
		const start1Minutes = timeToMinutes(start1);
		const end1Minutes = timeToMinutes(end1);
		const start2Minutes = timeToMinutes(start2);
		const end2Minutes = timeToMinutes(end2);

		// Ranges overlap if one starts before the other ends and vice versa
		// But they don't overlap if one ends exactly when the other starts
		return start1Minutes < end2Minutes && start2Minutes < end1Minutes;
	}

	// Function to validate day selection
	function validateDaySelection() {
		const activeDays = days.filter(day => day.active);
		hasNoDaysSelected = activeDays.length === 0;
		
		if (hasNoDaysSelected) {
			daySelectionError = t('at_least_one_day_required');
		} else {
			daySelectionError = '';
		}
		
		return !hasNoDaysSelected;
	}

	// Function to validate time range for a specific day
	function validateDayTimes(dayIndex) {
		const day = days[dayIndex];
		if (!day || !day.active) {
			// Clear any existing errors for this day
			delete timeValidationErrors[dayIndex];
			timeValidationErrors = { ...timeValidationErrors };
			return true;
		}

		// Check if active day has no times - this is invalid
		if (day.active && (!day.times || day.times.length === 0)) {
			timeValidationErrors[dayIndex] = ['Active day must have at least one time slot'];
			timeValidationErrors = { ...timeValidationErrors };
			return false;
		}

		let dayErrors = [];

		// Validate each time slot
		for (let i = 0; i < day.times.length; i++) {
			const timeSlot = day.times[i];

			if (!timeSlot.start || !timeSlot.end) {
				dayErrors.push(`Slot ${i + 1}: Both start and end times are required`);
				continue;
			}

			const startMinutes = timeToMinutes(timeSlot.start);
			const endMinutes = timeToMinutes(timeSlot.end);

			// Check if start time is before end time
			if (startMinutes >= endMinutes) {
				dayErrors.push(
					`Slot ${i + 1}: ${t('tab_schedule_error_start_time') || 'Start time must be before end time'}`
				);
				continue;
			}

			// Check for overlaps with other time slots on the same day
			for (let j = i + 1; j < day.times.length; j++) {
				const otherSlot = day.times[j];

				if (!otherSlot.start || !otherSlot.end) {
					continue; // Skip incomplete time slots
				}

				if (timeRangesOverlap(timeSlot.start, timeSlot.end, otherSlot.start, otherSlot.end)) {
					// dayErrors.push(`Slots ${i + 1} and ${j + 1}: Time ranges cannot overlap`);
					dayErrors.push(`${t('slot')} ${i + 1} ${t('and')} ${j + 1}: ${t('time_overlap')}`);
				}
			}
		}

		if (dayErrors.length > 0) {
			timeValidationErrors[dayIndex] = dayErrors;
			timeValidationErrors = { ...timeValidationErrors };
			return false;
		} else {
			delete timeValidationErrors[dayIndex];
			timeValidationErrors = { ...timeValidationErrors };
			return true;
		}
	}

	// Reactive statement to check if there are any validation errors
	$: hasValidationErrors = Object.keys(timeValidationErrors).length > 0 || hasNoDaysSelected;

	// Function to validate all days
	function validateAllDays() {
		let allValid = true;
		
		// Validate day selection first
		if (!validateDaySelection()) {
			allValid = false;
		}
		
		// Then validate time slots for each day
		for (let i = 0; i < days.length; i++) {
			if (!validateDayTimes(i)) {
				allValid = false;
			}
		}
		return allValid;
	}

	// Function to map day names to translation keys (adapted from BusinessHour.svelte)
	function getDayTranslationKey(dayName) {
		if (!dayName) return 'day_monday'; // fallback

		const normalizedDay = dayName.toLowerCase().trim();

		// Handle various day name formats
		const dayMapping = {
			monday: 'day_monday',
			mon: 'day_monday',
			tuesday: 'day_tuesday',
			tue: 'day_tuesday',
			wednesday: 'day_wednesday',
			wed: 'day_wednesday',
			thursday: 'day_thursday',
			thu: 'day_thursday',
			friday: 'day_friday',
			fri: 'day_friday',
			saturday: 'day_saturday',
			sat: 'day_saturday',
			sunday: 'day_sunday',
			sun: 'day_sunday',
			// Handle Thai day names in case server sends them
			วันจันทร์: 'day_monday',
			วันอังคาร: 'day_tuesday',
			วันพุธ: 'day_wednesday',
			วันพฤหัสบดี: 'day_thursday',
			วันศุกร์: 'day_friday',
			วันเสาร์: 'day_saturday',
			วันอาทิตย์: 'day_sunday'
		};

		return dayMapping[normalizedDay] || 'day_monday'; // fallback to Monday
	}

	// Function to mark form as having unsaved changes
	function markFormAsChanged() {
		formHasUnsavedChanges = true;
	}

	// Function to add a new time slot to a day
	function addTimeSlot(dayIndex) {
		if (!days[dayIndex].times) {
			days[dayIndex].times = [];
		}
		days[dayIndex].times.push({ start: '09:00', end: '18:00' });
		days = [...days]; // force reactivity
		markFormAsChanged();
		setTimeout(() => validateDayTimes(dayIndex), 0);
	}

	// Function to remove a time slot from a day
	function removeTimeSlot(dayIndex, timeIndex) {
		// Prevent removing the last time slot from an active day
		if (days[dayIndex].active && days[dayIndex].times.length <= 1) {
			// Instead of removing, just deactivate the day
			days[dayIndex].active = false;
			days[dayIndex].times = [];
		} else {
			days[dayIndex].times.splice(timeIndex, 1);
		}
		days = [...days]; // force reactivity
		markFormAsChanged();
		setTimeout(() => {
			validateDayTimes(dayIndex);
			validateDaySelection();
		}, 0);
	}

	// Fixed onMount function to load correct data based on toggle state
	onMount(() => {
		sameAsBusinessHours = user_schedule?.sameAsBusinessHours ?? false;

		if (sameAsBusinessHours && business_hours?.workShift) {
			// Load business hours when toggle is on
			days = JSON.parse(JSON.stringify(business_hours.workShift));
		} else if (user_schedule?.workShift) {
			// Load user's custom schedule when toggle is off
			days = JSON.parse(JSON.stringify(user_schedule.workShift));
		} else if (business_hours?.workShift) {
			// Fallback to business hours if no user schedule exists
			days = JSON.parse(JSON.stringify(business_hours.workShift));
		}

		// Ensure all days have times array and validate active days
		days = days.map((day) => {
			const updatedDay = {
				...day,
				times: day.times || []
			};
			// If day is active but has no times, add default time
			if (updatedDay.active && updatedDay.times.length === 0) {
				updatedDay.times = [{ start: '09:00', end: '18:00' }];
			}
			return updatedDay;
		});

		// Reset the unsaved changes flag when form is initialized with server data
		formHasUnsavedChanges = false;

		// Validate all days after loading
		setTimeout(() => validateAllDays(), 0);
	});

	function toggleDay(index) {
		days[index].active = !days[index].active;
		if (!days[index].active) {
			// Clear times when day is deactivated
			days[index].times = [];
		} else {
			// Always ensure active day has at least one time slot
			if (!days[index].times || days[index].times.length === 0) {
				days[index].times = [{ start: '09:00', end: '18:00' }];
			}
		}
		days = [...days]; // force reactivity
		markFormAsChanged(); // Mark form as changed
		// Validate the day after toggling and check day selection
		setTimeout(() => {
			validateDayTimes(index);
			validateDaySelection();
		}, 0);
	}

	function toggleSameAsBusinessHours() {
		if (sameAsBusinessHours && business_hours?.workShift) {
			// swap in the biz‐hours copy
			days = JSON.parse(JSON.stringify(business_hours.workShift));
		} else if (user_schedule?.workShift) {
			// Load user's custom schedule when toggle is turned off
			days = JSON.parse(JSON.stringify(user_schedule.workShift));
		}

		// Ensure all days have times array and validate active days
		days = days.map((day) => {
			const updatedDay = {
				...day,
				times: day.times || []
			};
			// If day is active but has no times, add default time
			if (updatedDay.active && updatedDay.times.length === 0) {
				updatedDay.times = [{ start: '09:00', end: '18:00' }];
			}
			return updatedDay;
		});

		formHasUnsavedChanges = true;

		// Validate all days after switching
		setTimeout(() => validateAllDays(), 0);
	}

	// Handle time change with validation
	function handleTimeChange(dayIndex, timeIndex, timeType) {
		days = [...days]; // force reactivity
		markFormAsChanged(); // Mark form as changed
		setTimeout(() => validateDayTimes(dayIndex), 0);
	}

	// Handle form submission with validation
	function handleSubmit(event) {
		if (!validateAllDays()) {
			event.preventDefault();
			return false;
		}
		return true;
	}

	// Enhanced form submission handler with toast notification
	function handleEnhance() {
		return async ({ result, update }) => {
			if (result.type === 'failure') {
				// Handle error case if needed
				console.error('Form submission failed:', result.data?.error);
			} else if (result.type === 'success') {
				// Show success toast
				const successMessage =
					result.data?.res_msg ||
					t('work_shift_update_success') ||
					'Work schedule updated successfully';
				toastStore.add(successMessage, 'success');

				// Reset change tracking on successful submission
				formHasUnsavedChanges = false;

				// Update the page data
				invalidateAll();
			}

			// await update();
		};
	}
</script>

<form
	method="POST"
	action="?/update_user_work_schedule"
	class="space-y-4 rounded-lg bg-white p-6 shadow-md"
	on:submit={handleSubmit}
	use:enhance={handleEnhance}
>
	<div class="flex w-full items-center justify-between">
		<div class="flex items-center space-x-3">
			<h2 class="text-xl font-medium text-gray-700">
				{t('work_shift_title')}
			</h2>
			<!-- Day selection validation error message next to title -->
			{#if hasNoDaysSelected && !sameAsBusinessHours}
				<span class="text-sm font-medium text-red-600">
					*{daySelectionError}
				</span>
			{/if}
		</div>

		<!-- submit as a real form -->
		<Button
			type="submit"
			color="green"
			disabled={hasValidationErrors || !formHasUnsavedChanges}
			class="disabled:cursor-not-allowed disabled:opacity-20"
		>
			<CheckOutline class="mr-2 h-4 w-4" />
			{t('update')}
		</Button>
	</div>

	<div>
		<p class="mb-2 text-sm text-gray-600">
			{t('work_shift_description_1')}
		</p>
		<p class="mb-2 text-sm text-gray-600">
			{t('work_shift_description_2')}
		</p>
	</div>

	<div class="mb-8 flex items-center">
		<span class="mr-2">{t('same_as_business_hours')}</span>
		<Toggle
			color="green"
			bind:checked={sameAsBusinessHours}
			on:change={toggleSameAsBusinessHours}
		/>
	</div>

	<!-- these hidden fields will POST your JSON -->
	<input type="hidden" name="sameAsBusinessHours" value={sameAsBusinessHours} />

	<input
		type="hidden"
		name="workShiftData"
		value={JSON.stringify(
			sameAsBusinessHours && business_hours?.workShift ? business_hours.workShift : days
		)}
	/>

	{#if sameAsBusinessHours}
		<!-- Read-only display of business hours with consistent styling -->
		<div class="space-y-4">
			{#if business_hours?.workShift && business_hours.workShift.length > 0}
				{#each business_hours.workShift as day, index}
					<div class="mb-4">
						<!-- Mobile layout: Day checkbox and label stacked above time controls -->
						<div class="flex flex-col space-y-2 md:hidden">
							<div class="flex items-center">
								<div class="w-10">
									<!-- Read-only checkbox (visual indicator only) -->
									<Checkbox type="checkbox" color="green" id="readonly-day-{index}" disabled />
								</div>
								<label for="readonly-day-{index}" class="w-32 font-medium text-gray-700">
									{t(getDayTranslationKey(day.day))}
								</label>
							</div>

							{#if day.active && day.times && day.times.length > 0}
								<!-- Time display - mobile stacked layout -->
								<div class="ml-10 space-y-2">
									{#each day.times as timeSlot, timeIndex}
										<div class="flex items-center space-x-2">
											<div class="relative inline-block w-full">
												<span
													class="block w-full appearance-none rounded border border-gray-300 bg-gray-50 px-4 py-2 pr-8 leading-tight text-gray-700"
												>
													{timeSlot.start}
												</span>
											</div>

											<span class="flex text-center text-gray-600">{t('to')}</span>

											<div class="relative inline-block w-full">
												<span
													class="block w-full appearance-none rounded border border-gray-300 bg-gray-50 px-4 py-2 pr-8 leading-tight text-gray-700"
												>
													{timeSlot.end}
												</span>
											</div>
										</div>
									{/each}
								</div>
							{:else if day.active}
								<div class="ml-10 flex flex-1 items-center">
									<span class="italic text-gray-500">{t('no_times_set')}</span>
								</div>
							{/if}
						</div>

						<!-- Desktop layout: Day checkbox aligned with first time slot -->
						<div class="hidden md:block">
							{#if day.active && day.times && day.times.length > 0}
								{#each day.times as timeSlot, timeIndex}
									<div class="flex items-center space-x-4 {timeIndex > 0 ? 'mt-2' : ''}">
										{#if timeIndex === 0}
											<!-- Show checkbox and day name only on first row -->
											<div class="flex items-center">
												<div class="w-10">
													<Checkbox
														color="green"
														id="readonly-day-desktop-{index}"
														checked={day.active}
														disabled
													/>
												</div>
												<label
													for="readonly-day-desktop-{index}"
													class="w-32 font-medium text-gray-700"
												>
													{t(getDayTranslationKey(day.day))}
												</label>
											</div>
										{:else}
											<!-- Empty space to align with checkbox/label on subsequent rows -->
											<!-- <div class="w-42"></div> -->
											<div class="w-[168px]"></div>
										{/if}

										<div class="flex items-center space-x-3">
											<div class="relative inline-block w-40">
												<span
													class="block w-full appearance-none rounded border border-gray-300 bg-gray-50 px-4 py-2 pr-8 leading-tight text-gray-700"
												>
													{timeSlot.start}
												</span>
											</div>

											<span class="text-gray-600">{t('to')}</span>

											<div class="relative inline-block w-40">
												<span
													class="block w-full appearance-none rounded border border-gray-300 bg-gray-50 px-4 py-2 pr-8 leading-tight text-gray-700"
												>
													{timeSlot.end}
												</span>
											</div>
										</div>
									</div>
								{/each}
							{:else if day.active}
								<div class="flex items-center space-x-2">
									<div class="flex items-center">
										<div class="w-10">
											<Checkbox
												color="green"
												id="readonly-day-desktop-inactive-{index}"
												checked={day.active}
												disabled
											/>
										</div>
										<label
											for="readonly-day-desktop-inactive-{index}"
											class="w-32 font-medium text-gray-700"
										>
											{t(getDayTranslationKey(day.day))}
										</label>
									</div>
									<div class="flex flex-1 items-center">
										<span class="italic text-gray-500">{t('no_times_set')}</span>
									</div>
								</div>
							{:else}
								<div class="flex items-center space-x-2">
									<div class="flex items-center">
										<div class="w-10">
											<Checkbox
												color="green"
												id="readonly-day-desktop-inactive-{index}"
												checked={day.active}
												disabled
											/>
										</div>
										<label
											for="readonly-day-desktop-inactive-{index}"
											class="w-32 font-medium text-gray-700"
										>
											{t(getDayTranslationKey(day.day))}
										</label>
									</div>
								</div>
							{/if}
						</div>
					</div>
				{/each}
			{:else}
				<p class="text-gray-500">{t('loading_business_hours')}</p>
			{/if}
		</div>
	{:else}
		<!-- Editable form for custom work schedule -->
		<div class="space-y-4">
			{#each days as day, i}
				<div class="mb-4">
					<!-- Mobile layout: Day checkbox and label stacked above time controls -->
					<div class="flex flex-col space-y-2 md:hidden">
						<div class="flex items-center">
							<div class="w-10">
								<Checkbox
									color="green"
									id="day-{i}"
									checked={day.active}
									on:change={() => toggleDay(i)}
								/>
							</div>
							<label for="day-{i}" class="w-32 font-medium text-gray-700">
								{t(getDayTranslationKey(day.day))}
							</label>
						</div>

						{#if day.active}
							<div class="ml-10 space-y-2">
								{#if day.times && day.times.length > 0}
									{#each day.times as timeSlot, timeIndex}
										<div class="flex items-center space-x-2">
											<!-- Time selection - mobile stacked layout -->
											<div class="flex-1 space-y-2">
												<div class="relative inline-block w-full">
													<select
														bind:value={timeSlot.start}
														on:change={() => handleTimeChange(i, timeIndex, 'start')}
														class="block w-full appearance-none rounded border px-4 py-2 pr-8 leading-tight text-gray-700 focus:outline-none {timeValidationErrors[
															i
														]
															? 'border-red-500 focus:border-red-500'
															: 'border-gray-300 focus:border-blue-500'} bg-white"
													>
														{#each timeOptions as option}
															<option value={option}>{option}</option>
														{/each}
													</select>
												</div>

												<span class="flex text-center text-gray-600">{t('to')}</span>

												<div class="relative inline-block w-full">
													<select
														bind:value={timeSlot.end}
														on:change={() => handleTimeChange(i, timeIndex, 'end')}
														class="block w-full appearance-none rounded border px-4 py-2 pr-8 leading-tight text-gray-700 focus:outline-none {timeValidationErrors[
															i
														]
															? 'border-red-500 focus:border-red-500'
															: 'border-gray-300 focus:border-blue-500'} bg-white"
													>
														{#each timeOptions as option}
															<option value={option}>{option}</option>
														{/each}
													</select>
												</div>
											</div>

											<!-- Remove button for mobile -->
											{#if day.times.length > 1}
												<button
													type="button"
													on:click={() => removeTimeSlot(i, timeIndex)}
													class="flex h-8 w-8 items-center justify-center rounded-full border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-200"
												>
													−
												</button>
											{/if}
										</div>
									{/each}
								{/if}

								<!-- Add time slot button - mobile -->
								<div class="flex items-center">
									<button
										type="button"
										on:click={() => addTimeSlot(i)}
										class="flex h-8 w-8 items-center justify-center rounded-full border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-200"
									>
										+
									</button>
									<span class="ml-2 text-sm text-gray-600">Add time slot</span>
								</div>
							</div>

							<!-- Time validation error for this specific day - mobile -->
							{#if timeValidationErrors[i]}
								<div class="ml-10 mt-2">
									<Alert color="red" class="px-3 py-2 text-sm">
										{#each timeValidationErrors[i] as error}
											<div>{error}</div>
										{/each}
									</Alert>
								</div>
							{/if}
						{/if}
					</div>

					<!-- Desktop layout: Day checkbox aligned with first time slot -->
					<div class="hidden md:block">
						{#if day.active && day.times && day.times.length > 0}
							{#each day.times as timeSlot, timeIndex}
								<div class="flex items-center space-x-4 {timeIndex > 0 ? 'mt-2' : ''}">
									{#if timeIndex === 0}
										<!-- Show checkbox and day name only on first row -->
										<div class="flex items-center">
											<div class="w-10">
												<Checkbox
													color="green"
													id="day-desktop-{i}"
													checked={day.active}
													on:change={() => toggleDay(i)}
												/>
											</div>
											<label for="day-desktop-{i}" class="w-32 font-medium text-gray-700">
												{t(getDayTranslationKey(day.day))}
											</label>
										</div>
									{:else}
										<!-- Empty space to align with checkbox/label on subsequent rows -->
										<!-- <div class="w-42"></div> -->
										<div class="w-[168px]"></div>
									{/if}

									<div class="flex items-center space-x-3">
										<div class="relative inline-block w-40">
											<select
												bind:value={timeSlot.start}
												on:change={() => handleTimeChange(i, timeIndex, 'start')}
												class="block w-full appearance-none rounded border px-4 py-2 pr-8 leading-tight text-gray-700 focus:outline-none {timeValidationErrors[
													i
												]
													? 'border-red-500 focus:border-red-500'
													: 'border-gray-300 focus:border-blue-500'} bg-white"
											>
												{#each timeOptions as option}
													<option value={option}>{option}</option>
												{/each}
											</select>
										</div>

										<span class="text-gray-600">{t('to')}</span>

										<div class="relative inline-block w-40">
											<select
												bind:value={timeSlot.end}
												on:change={() => handleTimeChange(i, timeIndex, 'end')}
												class="block w-full appearance-none rounded border px-4 py-2 pr-8 leading-tight text-gray-700 focus:outline-none {timeValidationErrors[
													i
												]
													? 'border-red-500 focus:border-red-500'
													: 'border-gray-300 focus:border-blue-500'} bg-white"
											>
												{#each timeOptions as option}
													<option value={option}>{option}</option>
												{/each}
											</select>
										</div>

										<!-- Add button for first time slot, remove button for others -->
										{#if timeIndex === 0}
											<button
												type="button"
												on:click={() => addTimeSlot(i)}
												class="flex h-8 w-8 items-center justify-center rounded-full border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-200"
											>
												+
											</button>
										{:else}
											<button
												type="button"
												on:click={() => removeTimeSlot(i, timeIndex)}
												class="flex h-8 w-8 items-center justify-center rounded-full border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-200"
											>
												−
											</button>
										{/if}
									</div>
								</div>
							{/each}
						{:else if day.active}
							<div class="flex items-center space-x-2">
								<div class="flex items-center">
									<div class="w-10">
										<Checkbox
											color="green"
											id="day-desktop-inactive-{i}"
											checked={day.active}
											on:change={() => toggleDay(i)}
										/>
									</div>
									<label for="day-desktop-inactive-{i}" class="w-32 font-medium text-gray-700">
										{t(getDayTranslationKey(day.day))}
									</label>
								</div>
								<div class="flex flex-1 items-center space-x-3">
									<span class="italic text-gray-500">{t('no_times_set')}</span>
									<button
										type="button"
										on:click={() => addTimeSlot(i)}
										class="flex h-8 w-8 items-center justify-center rounded-full border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-200"
									>
										+
									</button>
								</div>
							</div>
						{:else}
							<div class="flex items-center space-x-2">
								<div class="flex items-center">
									<div class="w-10">
										<Checkbox
											color="green"
											id="day-desktop-inactive-{i}"
											checked={day.active}
											on:change={() => toggleDay(i)}
										/>
									</div>
									<label for="day-desktop-inactive-{i}" class="w-32 font-medium text-gray-700">
										{t(getDayTranslationKey(day.day))}
									</label>
								</div>
							</div>
						{/if}
					</div>

					<!-- Time validation error for this specific day - desktop -->
					{#if timeValidationErrors[i]}
						<div class="hidden md:ml-52 md:mt-2 md:block">
							<Alert color="red" class="px-3 py-2 text-sm">
								{#each timeValidationErrors[i] as error}
									<div>{error}</div>
								{/each}
							</Alert>
						</div>
					{/if}
				</div>
			{/each}
		</div>
	{/if}
</form>